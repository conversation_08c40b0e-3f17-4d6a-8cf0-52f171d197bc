<template>
  <div>
    <!-- 无限滚动文本容器 - 应用新样式 -->
    <div class="scroll-alert-container">
      <div class="scroll-alert-content">
        <span class="alert-text"> <span class="alert-icon">⚠️</span>
          系统维护公告：为提供更优质的服务，我们将在未来较长时间内持续优化升级系统平台，在此期间可能出现短暂服务波动，对可能造成的不便我们深表歉意，如您遇到任何异常情况请及时联系技术支持团队。
        </span>
      </div>
    </div>

    <el-form class="login-form" status-icon :rules="loginRules" ref="loginForm" :model="loginForm">
      <!-- 原有表单内容保持不变 -->
      <el-form-item prop="username" label="用户名">
        <el-input @keyup.enter="handleLogin" v-model="loginForm.username" auto-complete="off"
                  :placeholder="$t('login.username')">
        </el-input>
      </el-form-item>
      <el-form-item prop="password" label="密码">
        <el-input @keyup.enter="handleLogin" :type="passwordType" v-model="loginForm.password" auto-complete="off"
                  :placeholder="$t('login.password')">
        </el-input>
      </el-form-item>
      <el-form-item v-if="this.website.captchaMode" prop="code" label="验证码">
        <el-row :span="24">
          <el-col :span="16">
            <el-input @keyup.enter="handleLogin" v-model="loginForm.code" auto-complete="off"
                      :placeholder="$t('login.code')">
            </el-input>
          </el-col>
          <el-col :span="8">
            <div class="login-code">
              <img :src="loginForm.image" class="login-code-img" @click="refreshCode" />
            </div>
          </el-col>
        </el-row>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click.prevent="handleLogin" class="login-submit">{{ $t('login.submit') }}
        </el-button>
      </el-form-item>
      <el-dialog title="用户信息选择" append-to-body v-model="userBox" width="350px">
        <avue-form :option="userOption" v-model="userForm" @submit="submitLogin" />
      </el-dialog>
    </el-form>

    <!-- 备案信息区域 -->
    <a href="https://beian.miit.gov.cn/" target="_blank" class="beian-container">
      <!-- 备案图标 -->
      <img
          src="https://beian.mps.gov.cn/img/logo01.dd7ff50e.png"
          alt="工信部备案图标"
          class="beian-icon"
          @error="handleIconError"
      />
      <!-- 备案号文字 -->
      <span class="beian-text">粤ICP备**********号-4</span>
    </a>
  </div>
</template>

<script>
import {mapGetters} from 'vuex';
import {info} from '@/api/system/tenant';
import {getCaptcha} from '@/api/user';
import {getTopUrl} from '@/utils/util';

export default {
  name: 'userlogin',
  data() {
    return {
      tenantMode: this.website.tenantMode,
      loginForm: {
        tenantId: '000000',
        deptId: '',
        roleId: '',
        username: '',
        password: '',
        type: 'account',
        code: '',
        key: '',
        image: 'data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7',
      },
      loginRules: {
        code: [{ required: true, message: '请输入验证码', trigger: 'blur' }],
        tenantId: [{ required: true, message: '请输入租户', trigger: 'blur' }],
        username: [{ required: true, message: '请输入用户名', trigger: 'blur' }],
        password: [
          { required: true, message: '请输入密码', trigger: 'blur' },
          { min: 1, message: '密码长度最少为6位', trigger: 'blur' },
        ],
      },
      passwordType: 'password',
      userBox: false,
      userForm: {
        deptId: '',
        roleId: '',
      },
      userOption: {
        labelWidth: 70,
        submitBtn: true,
        emptyBtn: false,
        submitText: '登录',
        column: [
          {
            label: '部门',
            prop: 'deptId',
            type: 'select',
            props: {
              label: 'deptName',
              value: 'id',
            },
            dicUrl: '/blade-system/dept/select',
            span: 24,
            display: false,
            rules: [
              {
                required: true,
                message: '请选择部门',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '角色',
            prop: 'roleId',
            type: 'select',
            props: {
              label: 'roleName',
              value: 'id',
            },
            dicUrl: '/blade-system/role/select',
            span: 24,
            display: false,
            rules: [
              {
                required: true,
                message: '请选择角色',
                trigger: 'blur',
              },
            ],
          },
        ],
      },
    };
  },
  created() {
    this.refreshCode();
  },
  mounted() { },
  watch: {
    'loginForm.deptId'() {
      const column = this.findObject(this.userOption.column, 'deptId');
      if (this.loginForm.deptId.includes(',')) {
        column.dicUrl = `/blade-system/dept/select?deptId=${this.loginForm.deptId}`;
        column.display = true;
      } else {
        column.dicUrl = '';
      }
    },
    'loginForm.roleId'() {
      const column = this.findObject(this.userOption.column, 'roleId');
      if (this.loginForm.roleId.includes(',')) {
        column.dicUrl = `/blade-system/role/select?roleId=${this.loginForm.roleId}`;
        column.display = true;
      } else {
        column.dicUrl = '';
      }
    },
  },
  computed: {
    ...mapGetters(['tagWel', 'userInfo']),
  },
  props: [],
  methods: {
    refreshCode() {
      if (this.website.captchaMode) {
        getCaptcha().then(res => {
          const data = res.data;
          this.loginForm.key = data.key;
          this.loginForm.image = data.image;
        });
      }
    },
    showPassword() {
      this.passwordType === '' ? (this.passwordType = 'password') : (this.passwordType = '');
    },
    submitLogin(form, done) {
      if (form.deptId !== '') {
        this.loginForm.deptId = form.deptId;
      }
      if (form.roleId !== '') {
        this.loginForm.roleId = form.roleId;
      }
      this.handleLogin();
      done();
    },
    handleLogin() {
      this.$refs.loginForm.validate(valid => {
        if (valid) {
          const loading = this.$loading({
            lock: true,
            text: '登录中,请稍后',
            background: 'rgba(0, 0, 0, 0.7)',
          });
          const rawCode = this.loginForm.code;
          this.loginForm.code = rawCode.trim().toUpperCase()
          console.log(this.loginForm.code)
          this.$store
              .dispatch('LoginByUsername', this.loginForm)
              .then(() => {
                if (this.website.switchMode) {
                  const deptId = this.userInfo.dept_id;
                  const roleId = this.userInfo.role_id;
                  if (deptId.includes(',') || roleId.includes(',')) {
                    this.loginForm.deptId = deptId;
                    this.loginForm.roleId = roleId;
                    this.userBox = true;
                    this.$store.dispatch('LogOut').then(() => {
                      loading.close();
                    });
                    return false;
                  }
                }
                loading.close();
                this.$router.push(this.tagWel);
              }).catch(err => {
            console.log(err);
            loading.close();
            this.refreshCode();
          });
        }
      });
    },
    getTenant() {
      let domain = getTopUrl();
      info(domain).then(res => {
        const data = res.data;
        if (data.success && data.data.tenantId) {
          this.tenantMode = false;
          this.loginForm.tenantId = data.data.tenantId;
          this.$parent.$refs.login.style.backgroundImage = `url(${data.data.backgroundUrl})`;
        }
      });
    },
    // 处理图标加载失败
    handleIconError(e) {
      // 图标加载失败时使用默认样式
      e.target.src = 'data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCI+PHBhdGggZmlsbD0iIzY2NiIgZD0iTTEyIDJDNi40OCAyIDIgNi40OCAyIDEyczQuNDggMTAgMTAgMTAgMTAtNC40OCAxMC0xMC00LjQ4LTEwLTEwLTEwem0wIDE4YzQuNDIgMCA4LTMuNTggOC04cy0zLjU4LTgtOC04LTgtMy41OCA4LTggOC04IDgtMy41OCA4LTggOC04eiIvPjxwYXRoIGZpbGw9IiM5OTkiIGQ9Ik0xMiA1Yy0uNTUgMC0xIC40NS0xIDEgMCAuNTUuNDUgMSAxIDFzMS0uNDUgMS0xLS40NS0xLTEtMXptMCAyYy41NSAwIDEgLjQ1IDEgMSAwIC41NS0uNDUgMS0xIDFzLTEtLjQ1LTEtMS41NS0xLTEtMUMyIDguNDUgMiA4LjQ1IDIgOC40NXMtMiAwLTEtLjU1eiIvPjwvc3ZnPg==';
    }
  },
};
</script>

<style scoped>
/* 警示公告条样式 */
.scroll-alert-container {
  width: 100%;
  max-width: 100vw;
  margin: 0 auto;
  overflow: hidden;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1000;

  /* 警示渐变背景 */
  background: linear-gradient(90deg, #b30000 0%, #8b0000 20%, #660000 80%);
  box-shadow: 0 4px 12px rgba(179, 0, 0, 0.4);

  /* 动态闪烁边框 */
  border-bottom: 2px solid #ff4d4d;
  animation: borderPulse 1.5s infinite alternate;
}

.scroll-alert-content {
  display: flex;
  padding: 8px 0;
  animation: scrollAlert 20s linear infinite;
  white-space: nowrap;
}

.scroll-alert-content:hover {
  animation-play-state: paused;
}

.alert-icon {
  margin: 0 0px;
  color: #fff;
  font-size: 1.4em;
  text-shadow: 0 0 8px rgba(255, 255, 255, 0.7);
  animation: blink 1s infinite;
}

.alert-text {
  font-family: 'Segoe UI', system-ui, sans-serif;
  font-size: 15px;
  font-weight: 600;
  color: #ffffff;
  letter-spacing: 0.8px;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.8);
}

/* 警示图标动画 */
@keyframes blink {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

/* 滚动动画 */
@keyframes scrollAlert {
  0% { transform: translateX(100%); }
  100% { transform: translateX(-85%); }
}

/* 边框脉动效果 */
@keyframes borderPulse {
  0% { border-color: #ff4d4d; }
  100% { border-color: #ff9999; }
}

/* 响应式调整 */
@media (max-width: 768px) {
  .scroll-alert-content {
    padding: 12px 0;
  }
  .alert-text {
    font-size: 16px;
    padding-right: 20px;
  }
}

/* 原有登录表单样式保持不变 */
.login-form {
  /* ...原有样式... */
}
/* 备案整体容器样式 */
.beian-container {
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  display: inline-flex; /* 保持内容自然宽度 */
  align-items: center;
  text-decoration: none; /* 去除默认链接下划线 */
  z-index: 10;
  padding: 4px 8px; /* 增加点击区域 */

  text-shadow: 0 0 2px rgba(0,0,0,0.8); /* 加阴影增强可读性 */
}

/* 备案图标样式 */
.beian-icon {
  width: 18px;
  height: 18px;
  margin-right: 5px;
  vertical-align: middle;
}

/* 备案文字样式 */
.beian-text {
  color: #a7adb1;
  font-size: 15px;
  transition: color 0.3s;
}

/* 整体悬停效果 */
.beian-container:hover .beian-text {
  color: #409eff; /* 文字变色 */
}

.beian-container:hover .beian-icon {
  opacity: 0.8; /* 图标轻微透明效果 */
}

</style>
<style></style>
