const WebSocket = require('ws');
const config = {
    port: 8099,
    timeoutMs: 60000,         // 连接超时时间（60秒）
    checkIntervalMs: 10000,   // 超时检查间隔（10秒）
    maxMessageSize: 1024 * 10 // 最大消息大小（10KB）
};

// 存储客户端连接
const clients = {
    computer: { ws: null, lastActive: 0 },
    miniPrograms: new Map()  // key: deviceNo, value: { ws, lastActive, token }
};

// 模拟验证令牌（实际应用中应从数据库或缓存中验证）
const verifyToken = (deviceNo, token) => {
    // 这里仅作示例，实际应使用安全的验证方式
    return token && token.startsWith(`valid_${deviceNo}`);
};

// 广播消息给所有小程序客户端
const broadcastToMiniPrograms = (message) => {
    clients.miniPrograms.forEach(client => {
        if (client.ws && client.ws.readyState === WebSocket.OPEN) {
            client.ws.send(message);
        }
    });
};

// 初始化WebSocket服务器
const wss = new WebSocket.Server({
    port: config.port,
    maxPayload: config.maxMessageSize
});

console.log(`WebSocket服务器已启动，监听端口 ${config.port}`);

// 定时检查超时连接
setInterval(() => {
    const now = Date.now();

    // 检查电脑客户端超时
    if (clients.computer.ws && now - clients.computer.lastActive > config.timeoutMs) {
        console.log('电脑客户端超时，断开连接');
        clients.computer.ws.close(4008, '连接超时');
        clients.computer.ws = null;

        // 通知所有小程序客户端电脑已离线
        broadcastToMiniPrograms(JSON.stringify({
            type: 'status',
            message: '电脑客户端已离线',
            status: 'computer_disconnected'
        }));
    }

    // 检查小程序客户端超时
    clients.miniPrograms.forEach((client, deviceNo) => {
        if (now - client.lastActive > config.timeoutMs) {
            console.log(`小程序设备 ${deviceNo} 超时，断开连接`);
            client.ws.close(4008, '连接超时');
            clients.miniPrograms.delete(deviceNo);
        }
    });
}, config.checkIntervalMs);

// 处理新连接
wss.on('connection', (ws) => {
    console.log('新客户端连接');
    let currentDeviceNo = null; // 当前连接的设备号（小程序）
    let isComputer = false;     // 是否为电脑客户端

    // 限制消息大小
    ws.on('message', (message, isBinary) => {
        if (isBinary) {
            ws.send(JSON.stringify({
                type: 'error',
                message: '不支持二进制消息'
            }));
            return;
        }

        // 检查消息大小
        if (message.length > config.maxMessageSize) {
            ws.send(JSON.stringify({
                type: 'error',
                message: `消息过大，最大支持 ${config.maxMessageSize} 字节`
            }));
            ws.close(4009, '消息过大');
            return;
        }

        processMessage(message);
    });

    // 处理消息
    const processMessage = (message) => {
        try {
            const data = JSON.parse(message.toString());
            console.log('收到消息:', data);

            // 更新活动时间
            updateLastActive();

            // 处理认证消息
            if (data.type === 'auth') {
                handleAuth(data);
                return;
            }

            // 未认证的客户端不能发送其他消息
            if (!isAuthenticated()) {
                ws.send(JSON.stringify({
                    type: 'error',
                    message: '请先完成认证'
                }));
                return;
            }

            // 新增：处理获取设备列表请求
            if (data.type === 'get_device_list' && isComputer) {
                const deviceList = [];
                clients.miniPrograms.forEach((client, deviceNo) => {
                    deviceList.push({
                        deviceNo: deviceNo,
                        lastActive: new Date(client.lastActive).toLocaleString(),
                        isConnected: client.ws.readyState === WebSocket.OPEN
                    });
                });

                ws.send(JSON.stringify({
                    type: 'device_list',
                    count: deviceList.length,
                    devices: deviceList,
                    timestamp: Date.now()
                }));
                return;
            }

            // 处理小程序主动剔除自身的请求
            if (data.type === 'self_kickout' && currentDeviceNo) {
                // 验证设备号是否匹配当前连接
                if (data.deviceNo === currentDeviceNo) {
                    // 关闭连接并清理
                    ws.close(4011, '设备主动请求剔除');
                    clients.miniPrograms.delete(currentDeviceNo);
                    console.log(`小程序设备 ${currentDeviceNo} 主动剔除成功`);

                    // 通知电脑端设备已主动断开
                    if (clients.computer.ws && clients.computer.ws.readyState === WebSocket.OPEN) {
                        clients.computer.ws.send(JSON.stringify({
                            type: 'device_self_kicked',
                            deviceNo: currentDeviceNo,
                            message: `设备 ${currentDeviceNo} 已主动断开连接`,
                            timestamp: Date.now()
                        }));
                    }
                } else {
                    ws.send(JSON.stringify({
                        type: 'error',
                        message: '设备号不匹配，无法剔除'
                    }));
                }
                return;
            }

            // 处理电脑客户端发送的剔除设备指令
            if (data.type === 'kickout_device' && isComputer) {
                if (!data.deviceNo) {
                    ws.send(JSON.stringify({
                        type: 'error',
                        message: '剔除失败：缺少设备号（deviceNo）'
                    }));
                    return;
                }

                const targetClient = clients.miniPrograms.get(data.deviceNo);
                if (targetClient?.ws) {
                    targetClient.ws.close(4010, data.reason || '被电脑客户端剔除');
                    clients.miniPrograms.delete(data.deviceNo);
                    ws.send(JSON.stringify({
                        type: 'confirm',
                        message: `已成功剔除设备 ${data.deviceNo}`
                    }));
                } else {
                    ws.send(JSON.stringify({
                        type: 'error',
                        message: `设备 ${data.deviceNo} 不存在或未连接`
                    }));
                }
                return;
            }

            // 处理心跳消息
            if (data.type === 'heartbeat') {
                ws.send(JSON.stringify({
                    type: 'heartbeat_ack',
                    timestamp: Date.now()
                }));
                return;
            }

            // 电脑客户端发送操作指令
            if (data.type === 'operation' && isComputer) {
                handleComputerOperation(data);
                return;
            }

            // 小程序客户端发送消息
            if (data.type === 'wx_message' && currentDeviceNo) {
                handleMiniProgramMessage(data);
                return;
            }

            // 未知消息类型
            ws.send(JSON.stringify({
                type: 'error',
                message: `未知消息类型: ${data.type}`
            }));

        } catch (e) {
            console.error('消息处理失败:', e);
            ws.send(JSON.stringify({
                type: 'error',
                message: '消息格式错误（请使用JSON格式）'
            }));
        }
    };

    // 处理认证
    const handleAuth = (data) => {
        // 电脑客户端认证
        if (data.role === 'computer') {
            if (clients.computer.ws) {
                clients.computer.ws.close(4002, '新的电脑客户端已连接');
            }

            clients.computer.ws = ws;
            clients.computer.lastActive = Date.now();
            isComputer = true;

            console.log('电脑客户端已认证并连接');
            ws.send(JSON.stringify({
                type: 'status',
                message: '已成功连接到服务器',
                status: 'connected'
            }));

            // 通知所有小程序客户端电脑已上线
            broadcastToMiniPrograms(JSON.stringify({
                type: 'status',
                message: '电脑客户端已连接',
                status: 'computer_connected'
            }));
            return;
        }

        // 小程序客户端认证
        if (data.role === 'miniProgram') {
            if (!data.deviceNo || !data.token) {
                ws.send(JSON.stringify({
                    type: 'error',
                    message: '认证失败：缺少设备号（deviceNo）或令牌（token）'
                }));
                ws.close(4001, '缺少必要认证信息');
                return;
            }

            if (!verifyToken(data.deviceNo, data.token)) {
                ws.send(JSON.stringify({
                    type: 'error',
                    message: '认证失败：令牌无效'
                }));
                ws.close(4003, '无效令牌');
                return;
            }

            if (clients.miniPrograms.has(data.deviceNo)) {
                const oldClient = clients.miniPrograms.get(data.deviceNo);
                oldClient.ws.close(4002, '同一设备的新连接已建立');
            }

            currentDeviceNo = data.deviceNo;
            clients.miniPrograms.set(currentDeviceNo, {
                ws: ws,
                lastActive: Date.now(),
                token: data.token
            });

            console.log(`小程序客户端（设备号：${currentDeviceNo}）已认证并连接`);
            ws.send(JSON.stringify({
                type: 'status',
                message: `已成功连接到服务器（设备号：${currentDeviceNo}）`,
                status: 'connected',
                computerConnected: !!clients.computer.ws
            }));

            // 通知电脑客户端有新设备上线
            if (clients.computer.ws && clients.computer.ws.readyState === WebSocket.OPEN) {
                clients.computer.ws.send(JSON.stringify({
                    type: 'device_online',
                    deviceNo: currentDeviceNo,
                    timestamp: Date.now()
                }));
            }
            return;
        }

        // 未知角色
        ws.send(JSON.stringify({
            type: 'error',
            message: `未知角色: ${data.role}`
        }));
        ws.close(4004, '未知角色');
    };

    // 处理电脑客户端操作指令
    const handleComputerOperation = (data) => {
        if (!data.targetDeviceNo) {
            ws.send(JSON.stringify({
                type: 'error',
                message: '操作失败：缺少目标设备号（targetDeviceNo）'
            }));
            return;
        }

        if (typeof data.data === 'undefined') {
            ws.send(JSON.stringify({
                type: 'error',
                message: '操作失败：缺少指令内容（data）'
            }));
            return;
        }

        const targetClient = clients.miniPrograms.get(data.targetDeviceNo);

        if (targetClient?.ws && targetClient.ws.readyState === WebSocket.OPEN) {
            targetClient.ws.send(JSON.stringify({
                type: 'operation',
                deviceNo: data.targetDeviceNo,
                command: data.data,
                timestamp: Date.now()
            }));

            ws.send(JSON.stringify({
                type: 'confirm',
                message: `操作指令已发送到设备 ${data.targetDeviceNo}`,
                timestamp: Date.now()
            }));
            console.log(`已向设备 ${data.targetDeviceNo} 发送指令`);
        } else {
            ws.send(JSON.stringify({
                type: 'error',
                message: `操作失败：设备 ${data.targetDeviceNo} 未连接或不存在`,
                timestamp: Date.now()
            }));
        }
    };

    // 处理小程序客户端消息
    const handleMiniProgramMessage = (data) => {
        if (typeof data.data === 'undefined') {
            ws.send(JSON.stringify({
                type: 'error',
                message: '消息发送失败：缺少消息内容（data）'
            }));
            return;
        }

        if (clients.computer.ws && clients.computer.ws.readyState === WebSocket.OPEN) {
            clients.computer.ws.send(JSON.stringify({
                type: 'wx_message',
                deviceNo: currentDeviceNo,
                data: data.data,
                timestamp: Date.now()
            }));

            ws.send(JSON.stringify({
                type: 'confirm',
                message: '消息已转发到电脑客户端',
                timestamp: Date.now()
            }));
            console.log(`设备 ${currentDeviceNo} 的消息已转发到电脑`);
        } else {
            ws.send(JSON.stringify({
                type: 'error',
                message: '消息发送失败：电脑客户端未连接',
                timestamp: Date.now()
            }));
        }
    };

    // 更新最后活动时间
    const updateLastActive = () => {
        if (isComputer && clients.computer.ws === ws) {
            clients.computer.lastActive = Date.now();
        } else if (currentDeviceNo && clients.miniPrograms.has(currentDeviceNo)) {
            const clientItem = clients.miniPrograms.get(currentDeviceNo);
            if (clientItem.ws === ws) {
                clientItem.lastActive = Date.now();
            }
        }
    };

    // 检查是否已认证
    const isAuthenticated = () => {
        return (isComputer && clients.computer.ws === ws) ||
            (currentDeviceNo && clients.miniPrograms.has(currentDeviceNo) &&
                clients.miniPrograms.get(currentDeviceNo).ws === ws);
    };

    // 处理连接关闭
    ws.on('close', (code, reason) => {
        console.log(`客户端断开连接（code: ${code}, reason: ${reason}）`);

        // 清理电脑客户端连接
        if (isComputer && clients.computer.ws === ws) {
            clients.computer.ws = null;
            console.log('电脑客户端已断开');

            broadcastToMiniPrograms(JSON.stringify({
                type: 'status',
                message: '电脑客户端已离线',
                status: 'computer_disconnected'
            }));
        }

        // 清理小程序客户端连接
        if (currentDeviceNo && clients.miniPrograms.has(currentDeviceNo) &&
            clients.miniPrograms.get(currentDeviceNo).ws === ws) {
            clients.miniPrograms.delete(currentDeviceNo);
            console.log(`小程序设备 ${currentDeviceNo} 已断开`);

            // 通知电脑客户端设备离线
            if (clients.computer.ws && clients.computer.ws.readyState === WebSocket.OPEN) {
                clients.computer.ws.send(JSON.stringify({
                    type: 'device_offline',
                    deviceNo: currentDeviceNo,
                    timestamp: Date.now()
                }));
            }
        }
    });

    // 处理错误
    ws.on('error', (error) => {
        console.error('WebSocket连接错误:', error);
    });
});

// 全局错误处理
process.on('uncaughtException', (error) => {
    console.error('未捕获的异常:', error);
});

process.on('unhandledRejection', (reason, promise) => {
    console.error('未处理的Promise拒绝:', reason, promise);
});

